# ChallengesKeystoneFrameUI.lua 使用说明

## 概述

这个文件是从 `temp.xml` 转换而来的挑战模式钥石框架UI的Lua实现。它完全重现了XML中定义的复杂UI结构，包括多层纹理、字体字符串和Atlas纹理系统。

## 功能特性

### 1. 完整的XML到Lua转换
- ✅ 主框架 (ChallengesKeystoneFrame) - 398x548像素，居中显示
- ✅ 多层纹理系统 (BACKGROUND, ARTWORK, OVERLAY)
- ✅ 子层级支持 (textureSubLevel 1-4)
- ✅ Atlas纹理集成，使用ExtractAtlasInfos插件
- ✅ 字体字符串 (FontString) 支持
- ✅ 复杂的锚点和定位系统

### 2. Atlas纹理处理
- 自动调用 `GetAtlasTextureInfo()` 获取纹理数据
- 正确设置纹理路径和UV坐标
- 支持 `useAtlasSize="true"` 属性
- 完整的错误处理和日志输出

### 3. UI元素层级结构

#### BACKGROUND层
- 主背景纹理 (ChallengeMode-KeystoneFrame)

#### ARTWORK层
- 符文背景 (ChallengeMode-RuneBG)
- 指令背景 (半透明黑色背景)

#### ARTWORK子层级1
- 爆发效果纹理 (ARTWORKBurst, BackgroundBurst)
- 分隔线 (ThinDivider)

#### ARTWORK子层级2
- 符文覆盖发光 (ARTWORKCoverGlow)

#### OVERLAY层
- 字体字符串：地牢名称、能量等级、时间限制、指令文本
- 纹理元素：五角星线条、圆圈发光、冲击波、符文等

#### OVERLAY子层级1
- 钥石槽背景 (KeystoneSlotBG)
- 5个符文圆圈发光效果 (顶部、右侧、右下、左下、左侧)

#### OVERLAY子层级3
- 钥石框架 (KeystoneSlotFrame)
- 5个方向的符文发光效果
- 大小符文发光

#### OVERLAY子层级4
- 钥石槽发光效果 (KeystoneSlotFrameGlow)

## 使用方法

### 1. 前置条件
确保 `ExtractAtlasInfos.lua` 插件已加载，并且 `GetAtlasTextureInfo` 函数可用。

### 2. 基本使用
```lua
-- 创建并显示UI
local frame = CreateAndShowChallengesKeystoneFrame()

-- 隐藏UI
if frame then
    frame:Hide()
end
```

### 3. 测试命令
在游戏聊天框中输入：
```
/script TestChallengesKeystoneFrame()
```

隐藏UI：
```
/script ChallengesKeystoneFrame:Hide()
```

## 技术实现细节

### 1. 函数结构
- `CreateAtlasTexture()` - 创建Atlas纹理的通用函数
- `SetupTextureWithAtlas()` - 设置纹理Atlas属性
- `CreateChallengesKeystoneFrame()` - 创建主框架和基础层
- `CreateOverlayElements()` - 创建OVERLAY层元素
- `CreateOverlaySubLevel1/3/4()` - 创建各子层级元素
- `CreateAndShowChallengesKeystoneFrame()` - 主入口函数

### 2. Atlas纹理处理
```lua
local atlasInfo = GetAtlasTextureInfo(atlasName)
if atlasInfo then
    texture:SetTexture(atlasInfo.atlasPath)
    texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
    if useAtlasSize then
        texture:SetSize(atlasInfo.width, atlasInfo.height)
    end
end
```

### 3. 错误处理
- 检查ExtractAtlasInfos插件可用性
- Atlas纹理加载失败时的警告信息
- 详细的日志输出用于调试

## 兼容性

- ✅ 魔兽世界 3.3.5 版本
- ✅ 依赖 ExtractAtlasInfos 插件
- ✅ 标准WoW UI框架API
- ✅ 完整的事件处理支持

## 注意事项

1. **依赖关系**：必须先加载 ExtractAtlasInfos.lua
2. **性能**：使用本地化变量提高性能
3. **内存**：框架创建后会保持在内存中，需要手动隐藏
4. **调试**：所有Atlas纹理加载都有日志输出

## 扩展功能

可以基于这个框架添加：
- 动画效果 (Alpha渐变、旋转等)
- 交互功能 (点击事件、拖拽等)
- 数据绑定 (显示实际的挑战模式数据)
- 自定义样式 (颜色、尺寸调整等)

## 故障排除

### 常见问题
1. **纹理不显示**：检查ExtractAtlasInfos插件是否正确加载
2. **框架不显示**：确保调用了 `frame:Show()`
3. **位置错误**：检查锚点设置和相对定位

### 调试方法
查看聊天框中的日志输出，所有Atlas纹理加载都会有相应的成功/失败信息。
