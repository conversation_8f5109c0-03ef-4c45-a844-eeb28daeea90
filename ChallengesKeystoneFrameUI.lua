ChallengesKeystoneFrameUI = {}

ChallengesKeystoneFrameMain = {}

local FrameUIMix = ChallengesKeystoneFrameUI

local FrameUI = nil

-- 创建Atlas纹理的辅助函数
-- @param parent Frame 父框架
-- @param atlasName string Atlas纹理名称
-- @param layer string 纹理层级 (可选)
-- @param sublevel number 子层级 (可选)
-- @return Texture 创建的纹理对象
function ChallengesKeystoneFrameUI:CreateAtlasTexture(parent, atlasName, layer, sublevel, useAtlasSize)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)
    
    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        -- 设置纹理路径和坐标
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
        print("|cff00ff00".. atlasName .."|r: 成功加载Atlas纹理 '" .. atlasName .. "'")
    else
        print("|cffff0000ChallengesKeystoneFrameUI|r: 警告 - 未找到Atlas纹理 '" .. atlasName .. "'")
    end

    if atlasInfo and useAtlasSize then
        print("|cff00ff00".. atlasName .. "|r: 使用Atlas原始尺寸 " .. atlasInfo.width .. "x" .. atlasInfo.height)
        texture:SetSize(atlasInfo.width, atlasInfo.height)
    end
    
    return texture
end

-- 设置纹理的Atlas属性
-- @param texture Texture 纹理对象
-- @param atlasName string Atlas纹理名称
-- @param useAtlasSize boolean 是否使用Atlas原始尺寸
function ChallengesKeystoneFrameUI:SetupTextureWithAtlas(texture, atlasName, useAtlasSize)
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        -- 设置纹理路径和坐标
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
        
        -- 如果指定使用Atlas原始尺寸，则应用尺寸
        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
        
        return true
    else
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 无法设置Atlas纹理 '" .. atlasName .. "'")
        return false
    end
end

function ChallengesKeystoneFrameUI:CreateChallengesKeystoneFrame()
    -- 创建主框架
    local frame = CreateFrame("Frame", "ChallengesKeystoneFrame", UIParent)
    frame:SetSize(398, 548)
    frame:SetPoint("CENTER", 0, 40)
    frame:Show() -- 对应XML中的hidden="true"
    frame:EnableMouse(true)

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 创建主框架 ChallengesKeystoneFrame")
    
    -- BACKGROUND层 - 主背景纹理
    local bgTexture = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-KeystoneFrame", "BACKGROUND")
    bgTexture:SetAllPoints(frame) -- 对应XML中的setAllPoints="true"
    
    -- ARTWORK层 - 符文背景
    local runeBG = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-RuneBG", "ARTWORK")
    runeBG:SetSize(360, 360)
    runeBG:SetPoint("CENTER", 0, 60)
    runeBG:SetAlpha(1)
    frame.RuneBG = runeBG -- 保存引用，供其他元素使用
    
    -- ARTWORK层 - 指令背景
    local instructionBG = FrameUIMix:CreateAtlasTexture(frame, "challenges-blackfade", "ARTWORK")
    instructionBG:SetSize(374, 60)
    instructionBG:SetPoint("BOTTOM", 0, 80)
    instructionBG:SetVertexColor(0, 0, 0, 0.8) -- 对应XML中的Color设置
    frame.InstructionBackground = instructionBG
    
    -- ARTWORK层子层级1 - 爆发效果纹理
    -- local bgBurst = CreateAtlasTexture(frame, "ChallengeMode-Runes-ARTWORKBurst", "ARTWORK", 1)
    -- SetupTextureWithAtlas(bgBurst, "ChallengeMode-Runes-ARTWORKBurst", true) -- useAtlasSize="true"
    -- bgBurst:SetPoint("CENTER", runeBG)
    -- bgBurst:SetAlpha(0)
    -- bgBurst:SetBlendMode("ADD")
    -- frame.BgBurst = bgBurst

    local bgBurst2 = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-BackgroundBurst", "ARTWORK", 1, true)
    -- FrameUI:SetupTextureWithAtlas(bgBurst2, "ChallengeMode-Runes-BackgroundBurst", true)
    bgBurst2:SetPoint("CENTER", 0, 61)
    bgBurst2:SetAlpha(0)
    bgBurst2:SetBlendMode("ADD")
    frame.BgBurst2 = bgBurst2
    
    local divider = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-ThinDivider", "ARTWORK", 1, true)
    divider:SetPoint("BOTTOM", instructionBG, "TOP")
    frame.Divider = divider
    
    -- -- ARTWORK层子层级2 - 符文覆盖发光
    -- local runeCoverGlow = FrameUI:CreateAtlasTexture(frame, "ChallengeMode-Runes-ARTWORKCoverGlow", "ARTWORK", 2, true)
    -- runeCoverGlow:SetPoint("CENTER", runeBG)
    -- runeCoverGlow:SetAlpha(1)
    -- frame.RuneCoverGlow = runeCoverGlow

    FrameUIMix:CreateOverlayElements(frame)
    FrameUIMix:CreateOverlaySubLevel1(frame)
    FrameUIMix:CreateOverlaySubLevel3(frame)
    FrameUIMix:CreateFrameElements(frame)

    -- 创建词缀框架（默认创建4个，对应常见的词缀数量）
    frame.AffixFrames = FrameUIMix:CreateMultipleAffixFrames(frame, 4, 0, -150, 60)

    FrameUIMix:CreateAnimationGroups(frame)
    FrameUIMix:SetAllFrameAlpha(frame)
    return frame
end

function ChallengesKeystoneFrameUI:CreateOverlayElements(frame)
    -- OVERLAY层 - 字体字符串
    local dungeonName = frame:CreateFontString(nil, "OVERLAY", "QuestFont_Enormous")
    dungeonName:SetSize(350, 0)
    dungeonName:SetPoint("BOTTOM", frame.Divider, "TOP", 0, 4)
    dungeonName:SetText("魔法回廊")
    dungeonName:Hide() -- 对应XML中的hidden="true"
    frame.DungeonName = dungeonName
    
    local powerLevel = frame:CreateFontString(nil, "OVERLAY", "QuestFont_Enormous")
    powerLevel:SetPoint("TOP", 0, -30)
    powerLevel:SetText("10lv")
    powerLevel:Hide()
    frame.PowerLevel = powerLevel
    
    local timeLimit = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightLarge")
    timeLimit:SetPoint("TOP", frame.Divider, "TOP", 0, -6)
    timeLimit:SetText("30分钟")
    timeLimit:Hide()
    frame.TimeLimit = timeLimit
    
    local instructions = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightLarge2")
    instructions:SetPoint("CENTER", frame.InstructionBackground)
    instructions:SetText("插入史诗钥石") -- 对应XML中的text属性
    frame.Instructions = instructions
    
    -- OVERLAY层 - 纹理元素     -- 五角星发光
    local pentagonLines = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-LineGlow", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(pentagonLines, "ChallengeMode-Runes-LineGlow", true)
    pentagonLines:SetPoint("CENTER", frame.RuneBG, 0, 6)
    pentagonLines:SetAlpha(0)
    pentagonLines:SetBlendMode("BLEND")
    frame.PentagonLines = pentagonLines
    
    -- 大圆圈发光
    local largeCircleGlow = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-InnerCircleGlow", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(largeCircleGlow, "ChallengeMode-Runes-InnerCircleGlow", true)
    largeCircleGlow:SetPoint("CENTER", frame.RuneBG, 0, 5)
    largeCircleGlow:SetAlpha(0)
    largeCircleGlow:SetBlendMode("BLEND")
    frame.LargeCircleGlow = largeCircleGlow
    
    -- 小圆圈发光
    local smallCircleGlow = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-SmallCircleGlow", "OVERLAY")
    smallCircleGlow:SetSize(130, 130) -- XML中指定了具体尺寸，不使用useAtlasSize
    smallCircleGlow:SetPoint("CENTER", frame.RuneBG, 0, 1)
    smallCircleGlow:SetAlpha(0)
    smallCircleGlow:SetBlendMode("BLEND")
    frame.SmallCircleGlow = smallCircleGlow
    
    -- 冲击波效果
    local shockwave = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-Shockwave", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(shockwave, "ChallengeMode-Runes-Shockwave", true)
    shockwave:SetPoint("CENTER", 0, 60)
    shockwave:SetAlpha(0)
    shockwave:SetBlendMode("ADD")
    frame.Shockwave = shockwave
    
    local shockwave2 = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-Shockwave", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(shockwave2, "ChallengeMode-Runes-Shockwave", true)
    shockwave2:SetPoint("CENTER", 0, 60)
    shockwave2:SetAlpha(0)
    shockwave2:SetBlendMode("ADD")
    frame.Shockwave2 = shockwave2
    
    -- 大符文
    local runesLarge = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-Large", "OVERLAY", 0, true)
    runesLarge:SetSize(196, 196)
    runesLarge:SetPoint("CENTER", 0, 61)
    runesLarge:SetAlpha(0.15)
    runesLarge:SetBlendMode("BLEND")
    frame.RunesLarge = runesLarge
    
    local glowBurstLarge = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowBurstLarge", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(glowBurstLarge, "ChallengeMode-Runes-GlowBurstLarge", true)
    glowBurstLarge:SetPoint("CENTER", runesLarge, -1, -3)
    glowBurstLarge:SetAlpha(0)
    glowBurstLarge:SetBlendMode("ADD")
    frame.GlowBurstLarge = glowBurstLarge
    
    -- 小符文
    local runesSmall = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-Small", "OVERLAY")
    runesSmall:SetSize(125, 125)
    runesSmall:SetPoint("CENTER", 0, 61)
    runesSmall:SetAlpha(0.15)
    runesSmall:SetBlendMode("BLEND")
    frame.RunesSmall = runesSmall
    
    local glowBurstSmall = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowBurstLarge", "OVERLAY", 0, true)
    -- SetupTextureWithAtlas(glowBurstSmall, "ChallengeMode-Runes-GlowBurstLarge", true)
    glowBurstSmall:SetPoint("CENTER", 0, 60)
    glowBurstSmall:SetAlpha(0)
    glowBurstSmall:SetBlendMode("BLEND")
    frame.GlowBurstSmall = glowBurstSmall
end

function ChallengesKeystoneFrameUI:CreateOverlaySubLevel1(frame)
    -- 钥石槽背景
    local slotBG = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotBG", "OVERLAY", 1, true)
    slotBG:SetPoint("CENTER", 0, 61)
    slotBG:SetAlpha(1)
    slotBG:SetBlendMode("BLEND")
    frame.SlotBG = slotBG

    -- 符文圆圈发光效果 - 顶部
    local runeCircleT = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleT:SetSize(48, 48)
    runeCircleT:SetPoint("CENTER", frame.RuneBG, "CENTER", 0, 126)
    runeCircleT:SetAlpha(0)
    runeCircleT:SetBlendMode("BLEND")
    frame.RuneCircleT = runeCircleT

    -- 符文圆圈发光效果 - 右侧
    local runeCircleR = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleR:SetSize(48, 48)
    runeCircleR:SetPoint("CENTER", frame.RuneBG, "CENTER", 118, 40)
    runeCircleR:SetAlpha(0)
    runeCircleR:SetBlendMode("BLEND")
    frame.RuneCircleR = runeCircleR

    -- 符文圆圈发光效果 - 右下
    local runeCircleBR = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleBR:SetSize(48, 48)
    runeCircleBR:SetPoint("CENTER", frame.RuneBG, "CENTER", 73, -98)
    runeCircleBR:SetAlpha(0)
    runeCircleBR:SetBlendMode("BLEND")
    frame.RuneCircleBR = runeCircleBR

    -- 符文圆圈发光效果 - 左下
    local runeCircleBL = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleBL:SetSize(48, 48)
    runeCircleBL:SetPoint("CENTER", frame.RuneBG, "CENTER", -73, -98)
    runeCircleBL:SetAlpha(0)
    runeCircleBL:SetBlendMode("BLEND")
    frame.RuneCircleBL = runeCircleBL

    -- 符文圆圈发光效果 - 左侧
    local runeCircleL = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleL:SetSize(48, 48)
    runeCircleL:SetPoint("CENTER", frame.RuneBG, "CENTER", -118, 40)
    runeCircleL:SetAlpha(0)
    runeCircleL:SetBlendMode("BLEND")
    frame.RuneCircleL = runeCircleL
end

function ChallengesKeystoneFrameUI:CreateOverlaySubLevel3(frame)
    -- 钥石框架
    local keystoneFrame = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotFrame", "OVERLAY", 3, true)
    keystoneFrame:SetPoint("CENTER", 0, 61)
    keystoneFrame:SetAlpha(1)
    keystoneFrame:SetBlendMode("ADD")
    frame.KeystoneFrame = keystoneFrame

    -- 各个方向的符文发光效果
    local runeT = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-T-Glow", "OVERLAY", 3)
    runeT:SetSize(40, 40)
    runeT:SetPoint("CENTER", frame.RuneCircleT)
    runeT:SetAlpha(0)
    runeT:SetBlendMode("BLEND")
    frame.RuneT = runeT

    local runeR = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-R-Glow", "OVERLAY", 3)
    runeR:SetSize(40, 40)
    runeR:SetPoint("CENTER", frame.RuneCircleR, "CENTER", -1, 0)
    runeR:SetAlpha(0)
    runeR:SetBlendMode("BLEND")
    frame.RuneR = runeR

    local runeBR = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-BR-Glow", "OVERLAY", 3)
    runeBR:SetSize(40, 40)
    runeBR:SetPoint("CENTER", frame.RuneCircleBR, "CENTER", -1, 0)
    runeBR:SetAlpha(0)
    runeBR:SetBlendMode("BLEND")
    frame.RuneBR = runeBR

    local runeBL = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-BL-Glow", "OVERLAY", 3)
    runeBL:SetSize(40, 40)
    runeBL:SetPoint("CENTER", frame.RuneCircleBL, "CENTER", -1, 0)
    runeBL:SetAlpha(0)
    runeBL:SetBlendMode("BLEND")
    frame.RuneBL = runeBL

    local runeL = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-L-Glow", "OVERLAY", 3)
    runeL:SetSize(40, 40)
    runeL:SetPoint("CENTER", frame.RuneCircleL)
    runeL:SetAlpha(0)
    runeL:SetBlendMode("BLEND")
    frame.RuneL = runeL

    -- 大符文发光
    local largeRuneGlow = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowLarge", "OVERLAY", 3)
    largeRuneGlow:SetSize(198, 199)
    largeRuneGlow:SetPoint("CENTER", 0, 61)
    largeRuneGlow:SetAlpha(0)
    largeRuneGlow:SetBlendMode("ADD")
    frame.LargeRuneGlow = largeRuneGlow

    -- 小符文发光
    local smallRuneGlow = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowSmall", "OVERLAY", 3)
    smallRuneGlow:SetSize(129, 129)
    smallRuneGlow:SetPoint("CENTER", 0, 61)
    smallRuneGlow:SetAlpha(0)
    smallRuneGlow:SetBlendMode("ADD")
    frame.SmallRuneGlow = smallRuneGlow

    local keystoneSlotGlow = FrameUIMix:CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotFrameGlow", "OVERLAY", 4, true)
    keystoneSlotGlow:SetPoint("CENTER", 0, 60)
    keystoneSlotGlow:SetAlpha(0)
    keystoneSlotGlow:SetBlendMode("ADD")
    frame.KeystoneSlotGlow = keystoneSlotGlow
end

-- 创建框架内的子框架元素（按钮等）
-- @param frame Frame 主框架对象
function ChallengesKeystoneFrameUI:CreateFrameElements (frame)
    -- 关闭按钮
    local closeButton = CreateFrame("Button", nil, frame, "UIPanelCloseButton")
    closeButton:SetPoint("TOPRIGHT", -4, -4)
    closeButton:SetScript("OnClick", function(self)
        self:GetParent():Hide()
    end)
    frame.CloseButton = closeButton

    -- 激活按钮
    local startButton = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    startButton:SetSize(120, 24)
    startButton:SetPoint("BOTTOM", 0, 20)
    startButton:SetText("激活")
    startButton:Disable() -- 对应XML中的enabled="false"
    startButton:SetScript("OnShow", function(self)
        self:SetWidth(self:GetTextWidth() + 60)
    end)
    frame.StartButton = startButton

    -- 钥石槽按钮
    local keystoneSlot = CreateFrame("Button", nil, frame)
    keystoneSlot:SetSize(48, 48)
    keystoneSlot:SetPoint("CENTER", frame.SlotBG)
    -- keystoneSlot:EnableMouse(true)

    -- 钥石槽纹理
    local keystoneTexture = keystoneSlot:CreateTexture(nil, "OVERLAY", nil, 2)
    keystoneTexture:SetAllPoints(keystoneSlot)
    keystoneSlot.Texture = keystoneTexture

    frame.KeystoneSlot = keystoneSlot

    -- 设置钥石槽的物品交互功能
    FrameUIMix:SetupFrameInteraction(frame)

    -- 初始化词缀数组
    frame.Affixes = {}

    -- 创建词缀模板管理器
    FrameUIMix:CreateAffixTemplateManager(frame)
end

-- 设置钥石槽的物品交互功能
-- @param frame Frame 主框架对象
function ChallengesKeystoneFrameUI:SetupFrameInteraction(frame)
    local keystoneSlot = frame.KeystoneSlot

    -- 存储当前钥石槽中的物品信息
    keystoneSlot.itemID = nil
    keystoneSlot.itemTexture = nil
    keystoneSlot.itemLink = nil
    
    -- 设置钥石槽的点击事件处理
    frame:SetScript("OnMouseUp", function(self, button)
        print("|cff00ff00ChallengesKeystoneFrameUI|r: 钥石槽已点击 : " .. button)

        if button == "LeftButton" then
            -- 检查光标上是否有物品
            local cursorType, itemID, itemLink = GetCursorInfo()

            if cursorType == "item" then
                -- 光标上有物品，尝试放置到钥石槽
                FrameUIMix:PlaceItemInKeystoneSlot(self.KeystoneSlot, itemID, itemLink)
                ClearCursor() -- 清除光标上的物品
            end
        end
    end)

    keystoneSlot:SetScript("OnClick", function(self, button)
        if button == "LeftButton" then
            local cursorType, itemID, itemLink = GetCursorInfo()

            if cursorType == "item" then
                FrameUIMix:PlaceItemInKeystoneSlot(self, itemID, itemLink)
                ClearCursor() -- 清除光标上的物品
            elseif self.itemID then
                -- 钥石槽中有物品，点击取出
                FrameUIMix:RemoveItemFromKeystoneSlot(self)
                FrameUIMix:ResetAllFrameAlpha(frame)
            end
        elseif button == "RightButton" and self.itemID then
            -- 右键点击移除物品
            FrameUIMix:RemoveItemFromKeystoneSlot(self)
            FrameUIMix:ResetAllFrameAlpha(frame)
        end
    end)
    

    -- 设置鼠标悬停事件显示物品tooltip
    keystoneSlot:SetScript("OnEnter", function(self)
        if self.itemLink then
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:SetHyperlink(self.itemLink)
            GameTooltip:Show()
        end
    end)

    -- 设置鼠标离开事件隐藏tooltip
    keystoneSlot:SetScript("OnLeave", function(self)
        if GameTooltip then
            GameTooltip:Hide()
        end
    end)

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 钥石槽交互功能已设置")
end

-- 创建词缀模板管理器
-- @param frame Frame 主框架对象
function ChallengesKeystoneFrameUI:CreateAffixTemplateManager(frame)
    -- 词缀模板创建函数
    frame.CreateAffixTemplate = function(parent, index)
        return FrameUIMix:CreateAffixTemplate(parent, index)
    end

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 词缀模板管理器已创建")
end

-- 创建词缀模板框架（对应XML中的ChallengesKeystoneFrameAffixTemplate）
-- @param parent Frame 父框架
-- @param index number 词缀索引
-- @return Frame 创建的词缀框架
function ChallengesKeystoneFrameUI:CreateAffixTemplate(parent, index)
    -- 创建词缀框架
    local affixFrame = CreateFrame("Frame", nil, parent)
    affixFrame:SetSize(52, 52) -- 对应XML中的Size x="52" y="52"
    affixFrame:EnableMouse(true) -- 对应XML中的enableMouse="true"

    -- OVERLAY层 - 边框纹理
    local border = FrameUIMix:CreateAtlasTexture(affixFrame, "ChallengeMode-AffixRing-Lg", "OVERLAY")
    border:SetAllPoints(affixFrame) -- 对应XML中的setAllPoints="true"
    affixFrame.Border = border -- 对应XML中的parentKey="Border"

    -- OVERLAY层 - 百分比字体字符串
    local percent = affixFrame:CreateFontString(nil, "OVERLAY", "SystemFont_Shadow_Large_Outline")
    percent:SetPoint("BOTTOM", border, "BOTTOM", 0, -4) -- 对应XML中的锚点设置
    affixFrame.Percent = percent -- 对应XML中的parentKey="Percent"

    -- ARTWORK层 - 肖像纹理
    local portrait = affixFrame:CreateTexture(nil, "ARTWORK")
    portrait:SetSize(50, 50) -- 对应XML中的Size x="50" y="50"
    portrait:SetPoint("CENTER", border, "CENTER") -- 对应XML中的锚点设置
    affixFrame.Portrait = portrait -- 对应XML中的parentKey="Portrait"

    -- 设置事件脚本
    -- OnEnter事件 - 对应XML中的OnEnter method="OnEnter"
    affixFrame:SetScript("OnEnter", function(self)
        FrameUIMix:AffixOnEnter(self)
    end)

    -- OnLeave事件 - 对应XML中的OnLeave function="GameTooltip_Hide"
    affixFrame:SetScript("OnLeave", function()
        GameTooltip_Hide()
    end)

    -- 将词缀框架添加到父框架的Affixes数组中（对应XML中的parentArray="Affixes"）
    if not parent.Affixes then
        parent.Affixes = {}
    end
    table.insert(parent.Affixes, affixFrame)

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 词缀模板框架已创建，索引: " .. (index or #parent.Affixes))

    return affixFrame
end

-- 词缀框架的OnEnter事件处理函数
-- @param affixFrame Frame 词缀框架
function ChallengesKeystoneFrameUI:AffixOnEnter(affixFrame)
    -- 这里可以添加词缀的鼠标悬停逻辑
    -- 例如显示词缀详细信息的tooltip
    if affixFrame.affixID then
        GameTooltip:SetOwner(affixFrame, "ANCHOR_RIGHT")
        -- 这里可以根据affixID设置具体的tooltip内容
        GameTooltip:SetText("词缀信息")
        GameTooltip:AddLine("词缀ID: " .. affixFrame.affixID, 1, 1, 1)
        if affixFrame.Percent:GetText() then
            GameTooltip:AddLine("等级: " .. affixFrame.Percent:GetText(), 1, 1, 1)
        end
        GameTooltip:Show()
    end
end

-- 创建多个词缀框架的辅助函数
-- @param parent Frame 父框架
-- @param count number 要创建的词缀框架数量
-- @param startX number 起始X坐标
-- @param startY number 起始Y坐标
-- @param spacing number 词缀框架之间的间距
-- @return table 创建的词缀框架数组
function ChallengesKeystoneFrameUI:CreateMultipleAffixFrames(parent, count, startX, startY, spacing)
    local affixFrames = {}

    for i = 1, count do
        local affixFrame = FrameUIMix:CreateAffixTemplate(parent, i)

        -- 设置位置（水平排列）
        if i == 1 then
            affixFrame:SetPoint("CENTER", parent, "CENTER", startX or 0, startY or -100)
        else
            affixFrame:SetPoint("LEFT", affixFrames[i-1], "RIGHT", spacing or 10, 0)
        end

        -- 初始隐藏
        affixFrame:Hide()

        table.insert(affixFrames, affixFrame)
    end

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 已创建 " .. count .. " 个词缀框架")

    return affixFrames
end

-- 设置词缀框架的数据
-- @param affixFrame Frame 词缀框架
-- @param affixID number 词缀ID
-- @param level number 词缀等级
-- @param iconTexture string 词缀图标纹理路径
function ChallengesKeystoneFrameUI:SetAffixData(affixFrame, affixID, level, iconTexture)
    if not affixFrame then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 词缀框架为空")
        return
    end

    -- 设置词缀数据
    affixFrame.affixID = affixID
    affixFrame.level = level

    -- 设置百分比文本（等级）
    if affixFrame.Percent and level then
        affixFrame.Percent:SetText(tostring(level))
    end

    -- 设置肖像纹理
    if affixFrame.Portrait and iconTexture then
        affixFrame.Portrait:SetTexture(iconTexture)
    end

    -- 显示词缀框架
    affixFrame:Show()

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 词缀数据已设置 - ID: " .. (affixID or "无") .. ", 等级: " .. (level or "无"))
end

-- 示例：设置词缀框架数据的函数
-- @param frame Frame 主框架
function ChallengesKeystoneFrameUI:SetExampleAffixData(frame)
    if not frame.AffixFrames then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 词缀框架未创建")
        return
    end

    -- 示例词缀数据
    local exampleAffixes = {
        {id = 10, level = 7, icon = "Interface\\Icons\\Ability_Fixated_State_Red"},  -- 暴怒
        {id = 11, level = 4, icon = "Interface\\Icons\\Spell_Shadow_Teleport"},      -- 爆裂
        {id = 12, level = 2, icon = "Interface\\Icons\\Ability_Warrior_Revenge"},    -- 死疽
        {id = 13, level = 15, icon = "Interface\\Icons\\Achievement_Boss_Archaedas"} -- 暴君
    }

    -- 设置每个词缀框架的数据
    for i, affixData in ipairs(exampleAffixes) do
        if frame.AffixFrames[i] then
            FrameUIMix:SetAffixData(frame.AffixFrames[i], affixData.id, affixData.level, affixData.icon)
        end
    end

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 示例词缀数据已设置")
end

-- 清除所有词缀框架数据
-- @param frame Frame 主框架
function ChallengesKeystoneFrameUI:ClearAffixData(frame)
    if not frame.AffixFrames then
        return
    end

    for _, affixFrame in ipairs(frame.AffixFrames) do
        affixFrame.affixID = nil
        affixFrame.level = nil

        if affixFrame.Percent then
            affixFrame.Percent:SetText("")
        end

        if affixFrame.Portrait then
            affixFrame.Portrait:SetTexture(nil)
        end

        affixFrame:Hide()
    end

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 词缀数据已清除")
end

-- 将物品放置到钥石槽中
-- @param keystoneSlot Button 钥石槽按钮
-- @param itemID number 物品ID
-- @param itemLink string 物品链接
function ChallengesKeystoneFrameUI:PlaceItemInKeystoneSlot(keystoneSlot, itemID, itemLink)
    if not itemID or not itemLink then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 无效的物品信息")
        return
    end

    -- 获取物品图标
    local itemTexture = GetItemIcon(itemID)
    if not itemTexture then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 无法获取物品图标")
        return
    end

    -- 存储物品信息
    keystoneSlot.itemID = itemID
    keystoneSlot.itemTexture = itemTexture
    keystoneSlot.itemLink = itemLink

    -- 设置钥石槽的纹理为物品图标
    SetPortraitToTexture(keystoneSlot.Texture, itemTexture)

    -- 获取物品名称用于日志
    local itemName = GetItemInfo(itemID)
    print("|cff00ff00ChallengesKeystoneFrameUI|r: 物品已放置到钥石槽: " .. (itemName or "未知物品") .. " (ID: " .. itemID .. ")")

    -- 播放钥石插入动画
    local frame = keystoneSlot:GetParent()
    if frame then
        FrameUIMix:PlayAllAnimations(frame)
    end
end

-- 从钥石槽中移除物品
-- @param keystoneSlot Button 钥石槽按钮
function ChallengesKeystoneFrameUI:RemoveItemFromKeystoneSlot(keystoneSlot)
    if not keystoneSlot.itemID then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 钥石槽中没有物品")
        return
    end

    -- 获取物品名称用于日志
    local itemName = GetItemInfo(keystoneSlot.itemID)
    print("|cff00ff00ChallengesKeystoneFrameUI|r: 物品已从钥石槽移除: " .. (itemName or "未知物品"))

    -- 清除物品信息
    keystoneSlot.itemID = nil
    keystoneSlot.itemTexture = nil
    keystoneSlot.itemLink = nil

    -- 清除钥石槽的纹理
    keystoneSlot.Texture:SetTexture(nil)

    -- 隐藏tooltip
    if GameTooltip then
        GameTooltip:Hide()
    end
end

function ChallengesKeystoneFrameUI:CreateAnimationGroups(frame)
    FrameUIMix:CreateInsertedAnim(frame)
    FrameUIMix:CreatePulseAnimation(frame)
    FrameUIMix:CreateRunesLargeRotateAnim(frame)
    FrameUIMix:CreateRunesLargeAnim(frame)
    FrameUIMix:CreateRunesSmallRotateAnim(frame)
    FrameUIMix:CreateRunesSmallAnim(frame)
end

function ChallengesKeystoneFrameUI:CreateInsertedAnim(frame)
    frame.KeystoneSlotGlowAnim = frame.KeystoneSlotGlow:CreateAnimationGroup()
    
    local KeystoneSlotGlowAnim1 = frame.KeystoneSlotGlowAnim:CreateAnimation("Alpha")
    KeystoneSlotGlowAnim1:SetDuration(0.15)
    KeystoneSlotGlowAnim1:SetChange(1)
    KeystoneSlotGlowAnim1:SetOrder(1)

    frame.PentagonLinesAnim = frame.PentagonLines:CreateAnimationGroup()

    local PentagonLinesAnim1 = frame.PentagonLinesAnim:CreateAnimation("Alpha")
    PentagonLinesAnim1:SetStartDelay(0.15)
    PentagonLinesAnim1:SetDuration(0.25)
    PentagonLinesAnim1:SetChange(1)
    PentagonLinesAnim1:SetSmoothing("IN")
    PentagonLinesAnim1:SetOrder(1)

    local PentagonLinesAnim2 = frame.PentagonLinesAnim:CreateAnimation("Alpha")
    PentagonLinesAnim2:SetStartDelay(0.55)
    PentagonLinesAnim2:SetDuration(1)
    PentagonLinesAnim2:SetChange(-0.45)
    PentagonLinesAnim2:SetSmoothing("IN_OUT")
    PentagonLinesAnim2:SetOrder(1)

    frame.LargeCircleGlowAnim = frame.LargeCircleGlow:CreateAnimationGroup()

    local LargeCircleGlowAnim1 = frame.LargeCircleGlowAnim:CreateAnimation("Alpha")
    LargeCircleGlowAnim1:SetStartDelay(0.05)
    LargeCircleGlowAnim1:SetDuration(0.25)
    LargeCircleGlowAnim1:SetChange(1)
    LargeCircleGlowAnim1:SetSmoothing("IN")
    LargeCircleGlowAnim1:SetOrder(1)

    local LargeCircleGlowAnim2 = frame.LargeCircleGlowAnim:CreateAnimation("Alpha")
    LargeCircleGlowAnim2:SetStartDelay(0.35)
    LargeCircleGlowAnim2:SetDuration(1)
    LargeCircleGlowAnim2:SetChange(-0.45)
    LargeCircleGlowAnim2:SetSmoothing("IN_OUT")
    LargeCircleGlowAnim2:SetOrder(1)

    frame.SmallCircleGlowAnim = frame.SmallCircleGlow:CreateAnimationGroup()

    local SmallCircleGlowAnim1 = frame.SmallCircleGlowAnim:CreateAnimation("Alpha")
    SmallCircleGlowAnim1:SetDuration(0.25)
    SmallCircleGlowAnim1:SetChange(1)
    SmallCircleGlowAnim1:SetSmoothing("IN")
    SmallCircleGlowAnim1:SetOrder(1)

    local SmallCircleGlowAnim2 = frame.SmallCircleGlowAnim:CreateAnimation("Alpha")
    SmallCircleGlowAnim2:SetStartDelay(0.25)
    SmallCircleGlowAnim2:SetDuration(1)
    SmallCircleGlowAnim2:SetChange(-0.45)
    SmallCircleGlowAnim2:SetSmoothing("IN_OUT")
    SmallCircleGlowAnim2:SetOrder(1)

    frame.RuneCircleTAnim = frame.RuneCircleT:CreateAnimationGroup()

    local RuneCircleTAlphaAnim1 = frame.RuneCircleTAnim:CreateAnimation("Alpha")
    RuneCircleTAlphaAnim1:SetStartDelay(0.35)
    RuneCircleTAlphaAnim1:SetDuration(0.35)
    RuneCircleTAlphaAnim1:SetChange(1)
    RuneCircleTAlphaAnim1:SetOrder(1)

    frame.RuneTAnim = frame.RuneT:CreateAnimationGroup()

    local RuneTAlphaAnim1 = frame.RuneTAnim:CreateAnimation("Alpha")
    RuneTAlphaAnim1:SetStartDelay(0.45)
    RuneTAlphaAnim1:SetDuration(0.45)
    RuneTAlphaAnim1:SetChange(1)
    RuneTAlphaAnim1:SetSmoothing("OUT")
    RuneTAlphaAnim1:SetOrder(1)

    frame.RuneCircleRAnim = frame.RuneCircleR:CreateAnimationGroup()

    local RuneCircleRAlphaAnim1 = frame.RuneCircleRAnim:CreateAnimation("Alpha")
    RuneCircleRAlphaAnim1:SetStartDelay(0.35)
    RuneCircleRAlphaAnim1:SetDuration(0.35)
    RuneCircleRAlphaAnim1:SetChange(1)
    RuneCircleRAlphaAnim1:SetOrder(1)

    frame.RunRAnim = frame.RuneR:CreateAnimationGroup()

    local RuneRAlphaAnim1 = frame.RunRAnim:CreateAnimation("Alpha")
    RuneRAlphaAnim1:SetStartDelay(0.45)
    RuneRAlphaAnim1:SetDuration(0.45)
    RuneRAlphaAnim1:SetChange(1)
    RuneRAlphaAnim1:SetSmoothing("OUT")
    RuneRAlphaAnim1:SetOrder(1)

    frame.RuneCircleBRAnim = frame.RuneCircleBR:CreateAnimationGroup()

    local RuneCircleBRAlphaAnim1 = frame.RuneCircleBRAnim:CreateAnimation("Alpha")
    RuneCircleBRAlphaAnim1:SetStartDelay(0.35)
    RuneCircleBRAlphaAnim1:SetDuration(0.35)
    RuneCircleBRAlphaAnim1:SetChange(1)
    RuneCircleBRAlphaAnim1:SetOrder(1)

    frame.RuneBRAnim = frame.RuneBR:CreateAnimationGroup()

    local RuneBRAlphaAnim1 = frame.RuneBRAnim:CreateAnimation("Alpha")
    RuneBRAlphaAnim1:SetStartDelay(0.45)
    RuneBRAlphaAnim1:SetDuration(0.45)
    RuneBRAlphaAnim1:SetChange(1)
    RuneBRAlphaAnim1:SetSmoothing("OUT")
    RuneBRAlphaAnim1:SetOrder(1)

    frame.RuneCircleBLAnim = frame.RuneCircleBL:CreateAnimationGroup()

    local RuneCircleBLAlphaAnim1 = frame.RuneCircleBLAnim:CreateAnimation("Alpha")
    RuneCircleBLAlphaAnim1:SetStartDelay(0.35)
    RuneCircleBLAlphaAnim1:SetDuration(0.35)
    RuneCircleBLAlphaAnim1:SetChange(1)
    RuneCircleBLAlphaAnim1:SetOrder(1)

    frame.RuneBLAnim = frame.RuneBL:CreateAnimationGroup()

    local RuneBLAlphaAnim1 = frame.RuneBLAnim:CreateAnimation("Alpha")
    RuneBLAlphaAnim1:SetStartDelay(0.45)
    RuneBLAlphaAnim1:SetDuration(0.45)
    RuneBLAlphaAnim1:SetChange(1)
    RuneBLAlphaAnim1:SetSmoothing("OUT")
    RuneBLAlphaAnim1:SetOrder(1)

    frame.RuneCircleLAnim = frame.RuneCircleL:CreateAnimationGroup()

    local RuneCircleLAlphaAnim1 = frame.RuneCircleLAnim:CreateAnimation("Alpha")
    RuneCircleLAlphaAnim1:SetStartDelay(0.35)
    RuneCircleLAlphaAnim1:SetDuration(0.35)
    RuneCircleLAlphaAnim1:SetChange(1)
    RuneCircleLAlphaAnim1:SetOrder(1)

    frame.RuneLAnim = frame.RuneL:CreateAnimationGroup()

    local RuneLAlphaAnim1 = frame.RuneLAnim:CreateAnimation("Alpha")
    RuneLAlphaAnim1:SetStartDelay(0.45)
    RuneLAlphaAnim1:SetDuration(0.45)
    RuneLAlphaAnim1:SetChange(1)
    RuneLAlphaAnim1:SetSmoothing("OUT")
    RuneLAlphaAnim1:SetOrder(1)

end

function ChallengesKeystoneFrameUI:PlayInsertedAnim(frame)
    frame.KeystoneSlotGlowAnim:Play()
    frame.PentagonLinesAnim:Play()
    frame.LargeCircleGlowAnim:Play()
    frame.SmallCircleGlowAnim:Play()
    frame.RuneCircleTAnim:Play()
    frame.RuneTAnim:Play()
    frame.RuneCircleRAnim:Play()
    frame.RunRAnim:Play()
    frame.RuneCircleBRAnim:Play()
    frame.RuneBRAnim:Play()
    frame.RuneCircleBLAnim:Play()
    frame.RuneBLAnim:Play()
    frame.RuneCircleLAnim:Play()
    frame.RuneLAnim:Play()
end

function ChallengesKeystoneFrameUI:CreatePulseAnimation(frame)
    frame.PulseAnim = frame.BgBurst2:CreateAnimationGroup()
    frame.PulseAnim:SetLooping("REPEAT")

    local bgBurst2AlphaAnim1 = frame.PulseAnim:CreateAnimation("Alpha")
    bgBurst2AlphaAnim1:SetDuration(1.5)     -- 设置动画持续时间
    bgBurst2AlphaAnim1:SetChange(0.75)        -- 从当前alpha值增加1（即从0.0变为1.0）
    bgBurst2AlphaAnim1:SetOrder(1)

    local bgBurst2AlphaAnim2 = frame.PulseAnim:CreateAnimation("Alpha")
    bgBurst2AlphaAnim2:SetStartDelay(1.5)   -- 设置延迟时间
    bgBurst2AlphaAnim2:SetDuration(1.5)     -- 设置动画持续时间
    bgBurst2AlphaAnim2:SetChange(-0.75)       -- 从当前alpha值减少1（即从1.0变为0.0）
    bgBurst2AlphaAnim2:SetOrder(1)
end

function ChallengesKeystoneFrameUI:CreateRunesLargeRotateAnim(frame)
    frame.RunesLargeRotateAnim = frame.RunesLarge:CreateAnimationGroup()
    frame.RunesLargeRotateAnim:SetLooping("REPEAT")

    local runesLargeRotateAnim = frame.RunesLargeRotateAnim:CreateAnimation("Rotation")
    runesLargeRotateAnim:SetDuration(60)
    runesLargeRotateAnim:SetDegrees(-360)
    runesLargeRotateAnim:SetOrder(1)

    frame.LargeRuneGlowRotateAnim = frame.LargeRuneGlow:CreateAnimationGroup()
    frame.LargeRuneGlowRotateAnim:SetLooping("REPEAT")

    local largeRuneGlowRotateAnim = frame.LargeRuneGlowRotateAnim:CreateAnimation("Rotation")
    largeRuneGlowRotateAnim:SetDuration(60)
    largeRuneGlowRotateAnim:SetDegrees(-360)
    largeRuneGlowRotateAnim:SetOrder(1)

    frame.GlowBurstLargeRotateAnim = frame.GlowBurstLarge:CreateAnimationGroup()
    frame.GlowBurstLargeRotateAnim:SetLooping("REPEAT")

    local glowBurstLargeRotateAnim = frame.GlowBurstLargeRotateAnim:CreateAnimation("Rotation")
    glowBurstLargeRotateAnim:SetDuration(60)
    glowBurstLargeRotateAnim:SetDegrees(-360)
    glowBurstLargeRotateAnim:SetOrder(1)
end

function ChallengesKeystoneFrameUI:PlayRunesLargeRotateAnim(frame)
    frame.RunesLargeRotateAnim:Play()
    frame.LargeRuneGlowRotateAnim:Play()
    frame.GlowBurstLargeRotateAnim:Play()
end

function ChallengesKeystoneFrameUI:CreateRunesLargeAnim(frame)
    frame.RunesLargeAnim = frame.RunesLarge:CreateAnimationGroup()

    local RunesLargeAlphaAnim = frame.RunesLargeAnim:CreateAnimation("Alpha")
    RunesLargeAlphaAnim:SetDuration(0.25)
    RunesLargeAlphaAnim:SetChange(1)
    RunesLargeAlphaAnim:SetSmoothing("OUT")
    RunesLargeAlphaAnim:SetOrder(1)

    frame.LargeRuneGlowAnim = frame.LargeRuneGlow:CreateAnimationGroup()

    local LargeRuneGlowAlphaAnim1 = frame.LargeRuneGlowAnim:CreateAnimation("Alpha")
    LargeRuneGlowAlphaAnim1:SetStartDelay(0.1)
    LargeRuneGlowAlphaAnim1:SetDuration(0.25)
    LargeRuneGlowAlphaAnim1:SetChange(1)
    LargeRuneGlowAlphaAnim1:SetSmoothing("OUT")
    LargeRuneGlowAlphaAnim1:SetOrder(1)    

    local LargeRuneGlowAlphaAnim2 = frame.LargeRuneGlowAnim:CreateAnimation("Alpha")
    LargeRuneGlowAlphaAnim2:SetStartDelay(0.6)
    LargeRuneGlowAlphaAnim2:SetDuration(1)
    LargeRuneGlowAlphaAnim2:SetChange(-1)
    LargeRuneGlowAlphaAnim2:SetSmoothing("IN")
    LargeRuneGlowAlphaAnim2:SetOrder(1)

    --这个动画效果的意义没搞明白
    frame.GlowBurstLargeAnim = frame.GlowBurstLarge:CreateAnimationGroup()
    --frame.GlowBurstLargeAnim:SetLooping("REPEAT")
    local GlowBurstLargeAlphaAnim1 = frame.GlowBurstLargeAnim:CreateAnimation("Alpha")
    GlowBurstLargeAlphaAnim1:SetStartDelay(0.25)
    GlowBurstLargeAlphaAnim1:SetDuration(0.25)
    GlowBurstLargeAlphaAnim1:SetChange(1)
    GlowBurstLargeAlphaAnim1:SetSmoothing("OUT")
    GlowBurstLargeAlphaAnim1:SetOrder(1)

    local GlowBurstLargeAlphaAnim2 = frame.GlowBurstLargeAnim:CreateAnimation("Alpha")
    GlowBurstLargeAlphaAnim2:SetStartDelay(0.5)
    GlowBurstLargeAlphaAnim2:SetDuration(0.5)
    GlowBurstLargeAlphaAnim2:SetChange(-1)
    GlowBurstLargeAlphaAnim2:SetSmoothing("IN")

    -- 可删除
    -- local GlowBurstLargeScaleAnim1 = frame.GlowBurstLargeAnim:CreateAnimation("Scale")
    -- GlowBurstLargeScaleAnim1:SetDuration(0.0)
    -- GlowBurstLargeScaleAnim1:SetScale(0.8, 0.8)
    -- GlowBurstLargeScaleAnim1:SetSmoothing("OUT")
    -- GlowBurstLargeScaleAnim1:SetOrder(1)

    local GlowBurstLargeScaleAnim = frame.GlowBurstLargeAnim:CreateAnimation("Scale")
    GlowBurstLargeScaleAnim:SetDuration(0.5)
    GlowBurstLargeScaleAnim:SetScale(1.0, 1.0)
    GlowBurstLargeScaleAnim:SetSmoothing("OUT")
    GlowBurstLargeScaleAnim:SetOrder(1)

end

function ChallengesKeystoneFrameUI:PlayRunesLargeAnim(frame)
    frame.RunesLargeAnim:Play()
    frame.LargeRuneGlowAnim:Play()
    frame.GlowBurstLargeAnim:Play()
end

function ChallengesKeystoneFrameUI:CreateRunesSmallRotateAnim(frame)
    frame.RunesSmallRotateAnim = frame.RunesSmall:CreateAnimationGroup()
    frame.RunesSmallRotateAnim:SetLooping("REPEAT")

    local runesSmallRotateAnim = frame.RunesSmallRotateAnim:CreateAnimation("Rotation")
    runesSmallRotateAnim:SetDuration(60)
    runesSmallRotateAnim:SetDegrees(360)
    runesSmallRotateAnim:SetOrder(1)

    frame.SmallRuneGlowRotateAnim = frame.SmallRuneGlow:CreateAnimationGroup()
    frame.SmallRuneGlowRotateAnim:SetLooping("REPEAT")

    local smallRuneGlowRotateAnim = frame.SmallRuneGlowRotateAnim:CreateAnimation("Rotation")
    smallRuneGlowRotateAnim:SetDuration(60)
    smallRuneGlowRotateAnim:SetDegrees(360)
    smallRuneGlowRotateAnim:SetOrder(1)

    frame.GlowBurstSmallRotateAnim = frame.GlowBurstSmall:CreateAnimationGroup()
    frame.GlowBurstSmallRotateAnim:SetLooping("REPEAT")

    local glowBurstSmallRotateAnim = frame.GlowBurstSmallRotateAnim:CreateAnimation("Rotation")
    glowBurstSmallRotateAnim:SetDuration(60)
    glowBurstSmallRotateAnim:SetDegrees(360)
    glowBurstSmallRotateAnim:SetOrder(1)

end

function ChallengesKeystoneFrameUI:PlayRunesSmallRotateAnim(frame)
    frame.RunesSmallRotateAnim:Play()
    frame.SmallRuneGlowRotateAnim:Play()
    frame.GlowBurstSmallRotateAnim:Play()
end

function ChallengesKeystoneFrameUI:CreateRunesSmallAnim(frame)
    frame.RunesSmallAnim = frame.RunesSmall:CreateAnimationGroup()

    local RunesSmallAlphaAnim = frame.RunesSmallAnim:CreateAnimation("Alpha")
    RunesSmallAlphaAnim:SetDuration(0.25)
    RunesSmallAlphaAnim:SetChange(1)
    RunesSmallAlphaAnim:SetSmoothing("OUT")
    RunesSmallAlphaAnim:SetOrder(1)

    frame.SmallRuneGlowAnim = frame.SmallRuneGlow:CreateAnimationGroup()
    
    local SmallRuneGlowAlphaAnim1 = frame.SmallRuneGlowAnim:CreateAnimation("Alpha")
    SmallRuneGlowAlphaAnim1:SetDuration(1)
    SmallRuneGlowAlphaAnim1:SetChange(1)
    SmallRuneGlowAlphaAnim1:SetOrder(1)

    local SmallRuneGlowAlphaAnim2 = frame.SmallRuneGlowAnim:CreateAnimation("Alpha")
    SmallRuneGlowAlphaAnim2:SetStartDelay(0.5)
    SmallRuneGlowAlphaAnim2:SetDuration(1)
    SmallRuneGlowAlphaAnim2:SetChange(-1)
    SmallRuneGlowAlphaAnim2:SetSmoothing("IN")
    SmallRuneGlowAlphaAnim2:SetOrder(1)

    frame.GlowBurstSmallAnim = frame.GlowBurstSmall:CreateAnimationGroup()

    local GlowBurstSmallAlphaAnim1 = frame.GlowBurstSmallAnim:CreateAnimation("Alpha")
    GlowBurstSmallAlphaAnim1:SetDuration(0.25)
    GlowBurstSmallAlphaAnim1:SetChange(1)
    GlowBurstSmallAlphaAnim1:SetSmoothing("OUT")
    GlowBurstSmallAlphaAnim1:SetOrder(1)

    local GlowBurstSmallAlphaAnim2 = frame.GlowBurstSmallAnim:CreateAnimation("Alpha")
    GlowBurstSmallAlphaAnim2:SetStartDelay(0.25)
    GlowBurstSmallAlphaAnim2:SetDuration(0.5)
    GlowBurstSmallAlphaAnim2:SetChange(-1)
    GlowBurstSmallAlphaAnim2:SetSmoothing("IN")
    GlowBurstSmallAlphaAnim2:SetOrder(1)

    local GlowBurstSmallScaleAnim = frame.GlowBurstSmallAnim:CreateAnimation("Scale")
    GlowBurstSmallScaleAnim:SetDuration(0.5)
    GlowBurstSmallScaleAnim:SetScale(0.65, 0.65)
    GlowBurstSmallScaleAnim:SetSmoothing("OUT")
    GlowBurstSmallScaleAnim:SetOrder(1)

end

function ChallengesKeystoneFrameUI:PlayRunesSmallAnim(frame)
    frame.RunesSmallAnim:Play()
    frame.SmallRuneGlowAnim:Play()
    frame.GlowBurstSmallAnim:Play()
end

function ChallengesKeystoneFrameUI:PlayAllAnimations(frame)
    FrameUIMix:PlayInsertedAnim(frame)
    frame.PulseAnim:Play()
    FrameUIMix:PlayRunesLargeRotateAnim(frame)
    FrameUIMix:PlayRunesLargeAnim(frame)
    FrameUIMix:PlayRunesSmallRotateAnim(frame)
    FrameUIMix:PlayRunesSmallAnim(frame)
    FrameUIMix:PlaySetAlphaAmin(frame)
end

function ChallengesKeystoneFrameUI:SetAllFrameAlpha(frame)
    -- 钥石插槽光圈效果设置透明度为1
    frame.KeystoneSlotGlowScaleAnim = frame.KeystoneSlotGlow:CreateAnimationGroup()

    local KeystoneSlotGlowScaleAnim1 = frame.KeystoneSlotGlowScaleAnim:CreateAnimation("Scale")
    KeystoneSlotGlowScaleAnim1:SetStartDelay(0.15)
    -- KeystoneSlotGlowScaleAnim1:SetDuration(0.15)
    KeystoneSlotGlowScaleAnim1:SetScale(1, 1)
    KeystoneSlotGlowScaleAnim1:SetOrder(1)
    
    frame.KeystoneSlotGlowScaleAnim:SetScript("OnFinished", function(self)
        frame.KeystoneSlotGlow:SetAlpha(1)
    end)

    -- 符文阵图五边形边长亮光效果设置透明度为1
    frame.PentagonLinesScaleAnim = frame.PentagonLines:CreateAnimationGroup()

    local PentagonLinesScaleAnim1 = frame.PentagonLinesScaleAnim:CreateAnimation("Scale")
    PentagonLinesScaleAnim1:SetStartDelay(1.55)
    PentagonLinesScaleAnim1:SetScale(1, 1)
    PentagonLinesScaleAnim1:SetOrder(1)

    frame.PentagonLinesScaleAnim:SetScript("OnFinished", function(self)
        frame.PentagonLines:SetAlpha(0.55)
    end)

    -- 大圆圈发光
    frame.LargeCircleGlowScaleAnim = frame.LargeCircleGlow:CreateAnimationGroup()

    local LargeCircleGlowScaleAnim1 = frame.LargeCircleGlowScaleAnim:CreateAnimation("Scale")
    LargeCircleGlowScaleAnim1:SetStartDelay(1.35)
    LargeCircleGlowScaleAnim1:SetScale(1, 1)
    LargeCircleGlowScaleAnim1:SetOrder(1)

    frame.LargeCircleGlowScaleAnim:SetScript("OnFinished", function(self)
        frame.LargeCircleGlow:SetAlpha(0.55)
    end)

    -- 小圆圈发光
    frame.SmallCircleGlowScaleAnim = frame.SmallCircleGlow:CreateAnimationGroup()

    local SmallCircleGlowScaleAnim1 = frame.SmallCircleGlowScaleAnim:CreateAnimation("Scale")
    SmallCircleGlowScaleAnim1:SetStartDelay(1.25)
    SmallCircleGlowScaleAnim1:SetScale(1, 1)
    SmallCircleGlowScaleAnim1:SetOrder(1)

    frame.SmallCircleGlowScaleAnim:SetScript("OnFinished", function(self)
        frame.SmallCircleGlow:SetAlpha(0.55)
    end)

    -- 大符文发光
    frame.LargeRuneGlowScaleAnim = frame.LargeRuneGlow:CreateAnimationGroup()

    local LargeRuneGlowScaleAnim1 = frame.LargeRuneGlowScaleAnim:CreateAnimation("Scale")
    LargeRuneGlowScaleAnim1:SetStartDelay(1.6)
    LargeRuneGlowScaleAnim1:SetScale(1, 1)
    LargeRuneGlowScaleAnim1:SetOrder(1)

    frame.LargeRuneGlowScaleAnim:SetScript("OnFinished", function(self)
        frame.LargeRuneGlow:SetAlpha(1)
    end)

    -- 小符文发光
    frame.SmallRuneGlowScaleAnim = frame.SmallRuneGlow:CreateAnimationGroup()

    local SmallRuneGlowScaleAnim1 = frame.SmallRuneGlowScaleAnim:CreateAnimation("Scale")
    SmallRuneGlowScaleAnim1:SetStartDelay(1.5)
    SmallRuneGlowScaleAnim1:SetScale(1, 1)
    SmallRuneGlowScaleAnim1:SetOrder(1)

    frame.SmallRuneGlowScaleAnim:SetScript("OnFinished", function(self)
        frame.SmallRuneGlow:SetAlpha(1)
    end)

    -- 顶部符文发光
    frame.RuneTScaleAnim = frame.RuneT:CreateAnimationGroup()

    local RuneTScaleAnim1 = frame.RuneTScaleAnim:CreateAnimation("Scale")
    RuneTScaleAnim1:SetStartDelay(0.9)
    RuneTScaleAnim1:SetScale(1, 1)
    RuneTScaleAnim1:SetOrder(1)

    frame.RuneTScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneT:SetAlpha(1)
    end)

    -- 右侧符文发光
    frame.RuneRScaleAnim = frame.RuneR:CreateAnimationGroup()

    local RuneRScaleAnim1 = frame.RuneRScaleAnim:CreateAnimation("Scale")
    RuneRScaleAnim1:SetStartDelay(0.9)
    RuneRScaleAnim1:SetScale(1, 1)
    RuneRScaleAnim1:SetOrder(1)

    frame.RuneRScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneR:SetAlpha(1)
    end)

    -- 右下符文发光
    frame.RuneBRScaleAnim = frame.RuneBR:CreateAnimationGroup()
    
    local RuneBRScaleAnim1 = frame.RuneBRScaleAnim:CreateAnimation("Scale")
    RuneBRScaleAnim1:SetStartDelay(0.9)
    RuneBRScaleAnim1:SetScale(1, 1)
    RuneBRScaleAnim1:SetOrder(1)

    frame.RuneBRScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneBR:SetAlpha(1)
    end)

    -- 左下符文发光
    frame.RuneBLScaleAnim = frame.RuneBL:CreateAnimationGroup()

    local RuneBLScaleAnim1 = frame.RuneBLScaleAnim:CreateAnimation("Scale")
    RuneBLScaleAnim1:SetStartDelay(0.9)
    RuneBLScaleAnim1:SetScale(1, 1)
    RuneBLScaleAnim1:SetOrder(1)

    frame.RuneBLScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneBL:SetAlpha(1)
    end)

    -- 左侧符文发光
    frame.RuneLScaleAnim = frame.RuneL:CreateAnimationGroup()
    
    local RuneLScaleAnim1 = frame.RuneLScaleAnim:CreateAnimation("Scale")
    RuneLScaleAnim1:SetStartDelay(0.9)
    RuneLScaleAnim1:SetScale(1, 1)
    RuneLScaleAnim1:SetOrder(1)

    frame.RuneLScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneL:SetAlpha(1)
    end)

    -- 顶部符文圆圈发光
    frame.RuneCircleTScaleAnim  = frame.RuneCircleT:CreateAnimationGroup()

    local RuneCircleTScaleAnim1 = frame.RuneCircleTScaleAnim:CreateAnimation("Scale")
    RuneCircleTScaleAnim1:SetStartDelay(0.7)
    RuneCircleTScaleAnim1:SetScale(1, 1)
    RuneCircleTScaleAnim1:SetOrder(1)

    frame.RuneCircleTScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneCircleT:SetAlpha(1)
    end)

    -- 右侧符文圆圈发光
    frame.RuneCircleRScaleAnim  = frame.RuneCircleR:CreateAnimationGroup()

    local RuneCircleRScaleAnim1 = frame.RuneCircleRScaleAnim:CreateAnimation("Scale")
    RuneCircleRScaleAnim1:SetStartDelay(0.7)
    RuneCircleRScaleAnim1:SetScale(1, 1)
    RuneCircleRScaleAnim1:SetOrder(1)

    frame.RuneCircleRScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneCircleR:SetAlpha(1)
    end)

    -- 右下符文圆圈发光
    frame.RuneCircleBRScaleAnim  = frame.RuneCircleBR:CreateAnimationGroup()
    
    local RuneCircleBRScaleAnim1 = frame.RuneCircleBRScaleAnim:CreateAnimation("Scale")
    RuneCircleBRScaleAnim1:SetStartDelay(0.7)
    RuneCircleBRScaleAnim1:SetScale(1, 1)
    RuneCircleBRScaleAnim1:SetOrder(1)

    frame.RuneCircleBRScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneCircleBR:SetAlpha(1)
    end)

    -- 左下符文圆圈发光
    frame.RuneCircleBLScaleAnim  = frame.RuneCircleBL:CreateAnimationGroup()
    
    local RuneCircleBLScaleAnim1 = frame.RuneCircleBLScaleAnim:CreateAnimation("Scale")
    RuneCircleBLScaleAnim1:SetStartDelay(0.7)
    RuneCircleBLScaleAnim1:SetScale(1, 1)
    RuneCircleBLScaleAnim1:SetOrder(1)

    frame.RuneCircleBLScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneCircleBL:SetAlpha(1)
    end)

    -- 左侧符文圆圈发光
    frame.RuneCircleLScaleAnim  = frame.RuneCircleL:CreateAnimationGroup()

    local RuneCircleLScaleAnim1 = frame.RuneCircleLScaleAnim:CreateAnimation("Scale")
    RuneCircleLScaleAnim1:SetStartDelay(0.7)
    RuneCircleLScaleAnim1:SetScale(1, 1)
    RuneCircleLScaleAnim1:SetOrder(1)

    frame.RuneCircleLScaleAnim:SetScript("OnFinished", function(self)
        frame.RuneCircleL:SetAlpha(1)
    end)
end

function ChallengesKeystoneFrameUI:PlaySetAlphaAmin(frame)
    frame.KeystoneSlotGlowScaleAnim:Play()
    frame.PentagonLinesScaleAnim:Play()
    frame.LargeCircleGlowScaleAnim:Play()
    frame.SmallCircleGlowScaleAnim:Play()
    frame.LargeRuneGlowScaleAnim:Play()
    frame.SmallRuneGlowScaleAnim:Play()
    frame.RuneTScaleAnim:Play()
    frame.RuneRScaleAnim:Play()
    frame.RuneBRScaleAnim:Play()
    frame.RuneBLScaleAnim:Play()
    frame.RuneLScaleAnim:Play()
    frame.RuneCircleTScaleAnim:Play()
    frame.RuneCircleRScaleAnim:Play()
    frame.RuneCircleBRScaleAnim:Play()
    frame.RuneCircleBLScaleAnim:Play()
    frame.RuneCircleLScaleAnim:Play()
    frame.Instructions:Hide()
    frame.DungeonName:Show()
    frame.PowerLevel:Show()
    frame.TimeLimit:Show()
end


function ChallengesKeystoneFrameUI:ResetAllFrameAlpha(frame)
    if frame.KeystoneSlotGlowScaleAnim:IsPlaying() then
        frame.KeystoneSlotGlowScaleAnim:Stop()
    end
    if frame.PentagonLinesScaleAnim:IsPlaying() then
        frame.PentagonLinesScaleAnim:Stop()
    end
    if frame.LargeCircleGlowScaleAnim:IsPlaying() then
        frame.LargeCircleGlowScaleAnim:Stop()
    end
    if frame.SmallCircleGlowScaleAnim:IsPlaying() then
        frame.SmallCircleGlowScaleAnim:Stop()
    end
    if frame.LargeRuneGlowScaleAnim:IsPlaying() then
        frame.LargeRuneGlowScaleAnim:Stop()
    end
    if frame.SmallRuneGlowScaleAnim:IsPlaying() then
        frame.SmallRuneGlowScaleAnim:Stop()
    end
    if frame.RuneTScaleAnim:IsPlaying() then
        frame.RuneTScaleAnim:Stop()
    end
    if frame.RuneRScaleAnim:IsPlaying() then
        frame.RuneRScaleAnim:Stop()
    end
    if frame.RuneBRScaleAnim:IsPlaying() then
        frame.RuneBRScaleAnim:Stop()
    end
    if frame.RuneBLScaleAnim:IsPlaying() then
        frame.RuneBLScaleAnim:Stop()
    end
    if frame.RuneLScaleAnim:IsPlaying() then
        frame.RuneLScaleAnim:Stop()
    end
    if frame.RuneCircleTScaleAnim:IsPlaying() then
        frame.RuneCircleTScaleAnim:Stop()
    end
    if frame.RuneCircleRScaleAnim:IsPlaying() then
        frame.RuneCircleRScaleAnim:Stop()
    end
    if frame.RuneCircleBRScaleAnim:IsPlaying() then
        frame.RuneCircleBRScaleAnim:Stop()
    end
    if frame.RuneCircleBLScaleAnim:IsPlaying() then
        frame.RuneCircleBLScaleAnim:Stop()
    end
    if frame.RuneCircleLScaleAnim:IsPlaying() then
        frame.RuneCircleLScaleAnim:Stop()
    end

    frame.KeystoneSlotGlow:SetAlpha(0)
    frame.PentagonLines:SetAlpha(0)
    frame.LargeCircleGlow:SetAlpha(0)
    frame.SmallCircleGlow:SetAlpha(0)
    frame.LargeRuneGlow:SetAlpha(0)
    frame.SmallRuneGlow:SetAlpha(0)
    frame.RuneT:SetAlpha(0)
    frame.RuneR:SetAlpha(0)
    frame.RuneBR:SetAlpha(0)
    frame.RuneBL:SetAlpha(0)
    frame.RuneL:SetAlpha(0)
    frame.RuneCircleT:SetAlpha(0)
    frame.RuneCircleR:SetAlpha(0)
    frame.RuneCircleBR:SetAlpha(0)
    frame.RuneCircleBL:SetAlpha(0)
    frame.RuneCircleL:SetAlpha(0)

    if frame.PulseAnim:IsPlaying() then
        frame.PulseAnim:Stop()
    end

    if frame.RunesLargeRotateAnim:IsPlaying() then
        frame.RunesLargeRotateAnim:Stop()
    end

    if frame.LargeRuneGlowRotateAnim:IsPlaying() then
        frame.LargeRuneGlowRotateAnim:Stop()
    end

    if frame.GlowBurstLargeRotateAnim:IsPlaying() then
        frame.GlowBurstLargeRotateAnim:Stop()
    end
    
    if frame.RunesSmallRotateAnim:IsPlaying() then
        frame.RunesSmallRotateAnim:Stop()
    end

    if frame.SmallRuneGlowRotateAnim:IsPlaying() then
        frame.SmallRuneGlowRotateAnim:Stop()
    end

    if frame.GlowBurstSmallRotateAnim:IsPlaying() then
        frame.GlowBurstSmallRotateAnim:Stop()
    end

    if not frame.Instructions:IsShown() then
        frame.Instructions:Show()
    end 

    if frame.DungeonName:IsShown() then 
        frame.DungeonName:Hide()
    end
    
    if frame.PowerLevel:IsShown() then
        frame.PowerLevel:Hide()
    end
    
    if frame.TimeLimit:IsShown() then
        frame.TimeLimit:Hide()
    end
    
end

function ChallengesKeystoneFrameUI:OnLoad()
    -- 注册事件
    self:RegisterEvent("ADDON_LOADED")
    
    -- 输出加载信息
    print("|cff00ff00ChallengesKeystoneFrameUI|r: 插件已加载")
end

function ChallengesKeystoneFrameUI:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        print("addonName : " .. addonName)
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            print("|cff00ff00ChallengesKeystoneFrameUI|r: 插件初始化完成")
            -- 注册斜杠命令
            self:RegisterCreateFrameCommand()
        end
    end
end

-- 注册创建框架的斜杠命令
function ChallengesKeystoneFrameUI:RegisterCreateFrameCommand()
    -- 注册 /createframe 命令
    SLASH_CREATEFRAME1 = "/createframe"
    SLASH_CREATEFRAME2 = "/showkeystone"

    SlashCmdList["CREATEFRAME"] = function()
        if FrameUI then
            if FrameUI:IsShown() then
                FrameUI:Hide()
            else
                FrameUI:Show()
                -- ChallengesKeystoneFrameUI:PlayAllAnimations(FrameUI)
            end
        else
            FrameUI = ChallengesKeystoneFrameUI:CreateChallengesKeystoneFrame()
            -- ChallengesKeystoneFrameUI:PlayAllAnimations(FrameUI)
        end

        print("|cff00ff00ChallengesKeystoneFrameUI|r: 挑战钥石界面已创建并显示")
    end

    -- 注册 /testaffix 命令用于测试词缀显示
    SLASH_TESTAFFIX1 = "/testaffix"
    SlashCmdList["TESTAFFIX"] = function()
        if FrameUI then
            FrameUIMix:SetExampleAffixData(FrameUI)
        else
            print("|cffff0000ChallengesKeystoneFrameUI|r: 请先创建框架 (/createframe)")
        end
    end

    -- 注册 /clearaffix 命令用于清除词缀显示
    SLASH_CLEARAFFIX1 = "/clearaffix"
    SlashCmdList["CLEARAFFIX"] = function()
        if FrameUI then
            FrameUIMix:ClearAffixData(FrameUI)
        else
            print("|cffff0000ChallengesKeystoneFrameUI|r: 请先创建框架 (/createframe)")
        end
    end

    print("|cff00ff00ChallengesKeystoneFrameUI|r: 斜杠命令已注册 - /createframe, /showkeystone, /testaffix, /clearaffix")
end

-- 创建框架用于事件处理
local frame = CreateFrame("Frame")
frame:SetScript("OnEvent", function(_, event, ...)
    ChallengesKeystoneFrameUI:OnEvent(event, ...)
end)

-- 注册事件
frame:RegisterEvent("ADDON_LOADED")